{"result": [{"scriptId": "1319", "url": "file:///E:/FX/scraping-analysis-marketing/src/test/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14658, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14658, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 329, "endOffset": 766, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 796, "endOffset": 4687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 874, "endOffset": 1213, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1257, "endOffset": 1471, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1504, "endOffset": 1784, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1829, "endOffset": 2109, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2143, "endOffset": 2223, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2271, "endOffset": 2351, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2392, "endOffset": 4669, "count": 26}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2433, "endOffset": 3637, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2478, "endOffset": 3105, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2535, "endOffset": 2711, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2763, "endOffset": 3069, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2827, "endOffset": 3025, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3149, "endOffset": 3415, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3459, "endOffset": 3609, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3674, "endOffset": 4125, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3723, "endOffset": 4097, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3780, "endOffset": 4061, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4162, "endOffset": 4387, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4207, "endOffset": 4359, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4424, "endOffset": 4649, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4723, "endOffset": 5159, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4858, "endOffset": 5003, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5033, "endOffset": 5149, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5859, "endOffset": 5954, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6010, "endOffset": 6105, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6231, "endOffset": 6522, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6571, "endOffset": 6649, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6695, "endOffset": 6801, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6850, "endOffset": 6884, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1939", "url": "file:///E:/FX/scraping-analysis-marketing/src/test/mocks/server.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7060, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7060, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 604, "endOffset": 675, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 752, "endOffset": 858, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 936, "endOffset": 1042, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1121, "endOffset": 1192, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1299, "endOffset": 1768, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1870, "endOffset": 2122, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2217, "endOffset": 2653, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2754, "endOffset": 2778, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2939, "endOffset": 2961, "count": 11}], "isBlockCoverage": true}]}, {"scriptId": "2024", "url": "file:///E:/FX/scraping-analysis-marketing/src/components/__tests__/ScraperForm.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 25018, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 25018, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 332, "endOffset": 797, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1366, "endOffset": 10800, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1387, "endOffset": 1426, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1466, "endOffset": 2090, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2123, "endOffset": 3209, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2954, "endOffset": 3201, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3241, "endOffset": 4316, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4062, "endOffset": 4308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4351, "endOffset": 5525, "count": 1}, {"startOffset": 5519, "endOffset": 5524, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5407, "endOffset": 5517, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5569, "endOffset": 6461, "count": 1}, {"startOffset": 6455, "endOffset": 6460, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6389, "endOffset": 6453, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6517, "endOffset": 7626, "count": 1}, {"startOffset": 7620, "endOffset": 7625, "count": 0}], "isBlockCoverage": true}, {"functionName": "__vi_import_1__.waitFor.timeout", "ranges": [{"startOffset": 7326, "endOffset": 7579, "count": 103}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7674, "endOffset": 8952, "count": 1}, {"startOffset": 8946, "endOffset": 8951, "count": 0}], "isBlockCoverage": true}, {"functionName": "__vi_import_1__.waitFor.timeout", "ranges": [{"startOffset": 8690, "endOffset": 8905, "count": 114}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8990, "endOffset": 9668, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9718, "endOffset": 10796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10525, "endOffset": 10788, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "2172", "url": "file:///E:/FX/scraping-analysis-marketing/src/test/utils.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19571, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19571, "count": 1}], "isBlockCoverage": true}, {"functionName": "AllTheProviders", "ranges": [{"startOffset": 1368, "endOffset": 3295, "count": 10}], "isBlockCoverage": true}, {"functionName": "customRender", "ranges": [{"startOffset": 3318, "endOffset": 3427, "count": 9}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3683, "endOffset": 3711, "count": 9}], "isBlockCoverage": true}, {"functionName": "createMockScrapingJob", "ranges": [{"startOffset": 3768, "endOffset": 4096, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4207, "endOffset": 4244, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockScrapedData", "ranges": [{"startOffset": 4278, "endOffset": 4832, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4943, "endOffset": 4980, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockSEOAnalysis", "ranges": [{"startOffset": 5014, "endOffset": 6860, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6971, "endOffset": 7008, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockKeywordData", "ranges": [{"startOffset": 7042, "endOffset": 7191, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7302, "endOffset": 7339, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockLeadData", "ranges": [{"startOffset": 7370, "endOffset": 7576, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7684, "endOffset": 7718, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockAnalyticsStats", "ranges": [{"startOffset": 7755, "endOffset": 7901, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8015, "endOffset": 8055, "count": 0}], "isBlockCoverage": false}, {"functionName": "waitForLoadingToFinish", "ranges": [{"startOffset": 8122, "endOffset": 8188, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8300, "endOffset": 8338, "count": 0}], "isBlockCoverage": false}, {"functionName": "mockConsoleError", "ranges": [{"startOffset": 8367, "endOffset": 8507, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8613, "endOffset": 8645, "count": 0}], "isBlockCoverage": false}, {"functionName": "mockConsoleWarn", "ranges": [{"startOffset": 8673, "endOffset": 8808, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8913, "endOffset": 8944, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2283", "url": "file:///E:/FX/scraping-analysis-marketing/src/components/theme-provider.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6847, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6847, "count": 1}], "isBlockCoverage": true}, {"functionName": "ThemeProvider", "ranges": [{"startOffset": 660, "endOffset": 2020, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 810, "endOffset": 1106, "count": 9}, {"startOffset": 953, "endOffset": 1032, "count": 0}, {"startOffset": 1033, "endOffset": 1045, "count": 0}, {"startOffset": 1071, "endOffset": 1105, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1145, "endOffset": 1512, "count": 9}, {"startOffset": 1285, "endOffset": 1471, "count": 0}], "isBlockCoverage": true}, {"functionName": "setTheme", "ranges": [{"startOffset": 1591, "endOffset": 1690, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2122, "endOffset": 2151, "count": 9}], "isBlockCoverage": true}, {"functionName": "useTheme", "ranges": [{"startOffset": 2172, "endOffset": 2366, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2464, "endOffset": 2488, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2284", "url": "file:///E:/FX/scraping-analysis-marketing/src/components/ui/tooltip.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4934, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4934, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 848, "endOffset": 1671, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1842, "endOffset": 1865, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1971, "endOffset": 2001, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2107, "endOffset": 2137, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2244, "endOffset": 2275, "count": 9}], "isBlockCoverage": true}]}, {"scriptId": "2311", "url": "file:///E:/FX/scraping-analysis-marketing/src/lib/utils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1328, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1328, "count": 1}], "isBlockCoverage": true}, {"functionName": "cn", "ranges": [{"startOffset": 448, "endOffset": 552, "count": 1834}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 643, "endOffset": 661, "count": 1834}], "isBlockCoverage": true}]}, {"scriptId": "2314", "url": "file:///E:/FX/scraping-analysis-marketing/src/contexts/AppContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 40841, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 40841, "count": 1}], "isBlockCoverage": true}, {"functionName": "useAppContext", "ranges": [{"startOffset": 719, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1018, "endOffset": 1047, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1071, "endOffset": 13202, "count": 25}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2620, "endOffset": 3619, "count": 9}], "isBlockCoverage": true}, {"functionName": "initializeApp", "ranges": [{"startOffset": 2656, "endOffset": 3587, "count": 9}, {"startOffset": 2977, "endOffset": 2982, "count": 0}, {"startOffset": 3032, "endOffset": 3038, "count": 0}, {"startOffset": 3100, "endOffset": 3112, "count": 0}, {"startOffset": 3481, "endOffset": 3577, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3689, "endOffset": 5736, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3792, "endOffset": 5681, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5699, "endOffset": 5729, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5859, "endOffset": 6504, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6570, "endOffset": 7357, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7420, "endOffset": 8000, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8072, "endOffset": 8989, "count": 9}, {"startOffset": 8889, "endOffset": 8983, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9060, "endOffset": 9478, "count": 9}, {"startOffset": 9315, "endOffset": 9327, "count": 0}, {"startOffset": 9362, "endOffset": 9367, "count": 0}, {"startOffset": 9380, "endOffset": 9472, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9570, "endOffset": 9620, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9688, "endOffset": 10145, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10233, "endOffset": 10277, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10365, "endOffset": 10654, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10723, "endOffset": 11317, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11389, "endOffset": 11689, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11765, "endOffset": 12205, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 13303, "endOffset": 13330, "count": 9}], "isBlockCoverage": true}]}, {"scriptId": "2315", "url": "file:///E:/FX/scraping-analysis-marketing/src/components/ScraperForm.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 68707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 68707, "count": 1}], "isBlockCoverage": true}, {"functionName": "ScraperForm", "ranges": [{"startOffset": 2225, "endOffset": 39838, "count": 177}, {"startOffset": 13198, "endOffset": 13209, "count": 176}, {"startOffset": 13210, "endOffset": 13261, "count": 0}, {"startOffset": 13857, "endOffset": 14368, "count": 0}, {"startOffset": 15349, "endOffset": 19661, "count": 1}, {"startOffset": 19706, "endOffset": 21845, "count": 1}, {"startOffset": 27986, "endOffset": 37306, "count": 7}, {"startOffset": 32926, "endOffset": 34788, "count": 1}, {"startOffset": 34848, "endOffset": 36702, "count": 1}, {"startOffset": 37629, "endOffset": 38453, "count": 7}, {"startOffset": 38454, "endOffset": 39173, "count": 169}], "isBlockCoverage": true}, {"functionName": "addUrl", "ranges": [{"startOffset": 3213, "endOffset": 3587, "count": 1}, {"startOffset": 3250, "endOffset": 3515, "count": 0}], "isBlockCoverage": true}, {"functionName": "removeUrl", "ranges": [{"startOffset": 3611, "endOffset": 3661, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateUrl", "ranges": [{"startOffset": 3685, "endOffset": 4036, "count": 142}], "isBlockCoverage": true}, {"functionName": "handleScrape", "ranges": [{"startOffset": 4063, "endOffset": 10358, "count": 7}, {"startOffset": 4133, "endOffset": 4440, "count": 0}, {"startOffset": 4535, "endOffset": 4685, "count": 1}, {"startOffset": 4685, "endOffset": 4855, "count": 6}, {"startOffset": 4855, "endOffset": 5303, "count": 1}, {"startOffset": 5303, "endOffset": 5340, "count": 5}, {"startOffset": 5340, "endOffset": 5502, "count": 0}, {"startOffset": 5502, "endOffset": 6005, "count": 5}, {"startOffset": 6005, "endOffset": 9715, "count": 6}, {"startOffset": 6479, "endOffset": 6588, "count": 0}, {"startOffset": 7054, "endOffset": 7140, "count": 1}, {"startOffset": 7140, "endOffset": 7558, "count": 5}, {"startOffset": 7558, "endOffset": 7670, "count": 0}, {"startOffset": 7670, "endOffset": 8086, "count": 5}, {"startOffset": 8086, "endOffset": 8091, "count": 0}, {"startOffset": 8149, "endOffset": 8154, "count": 0}, {"startOffset": 8220, "endOffset": 8224, "count": 0}, {"startOffset": 8284, "endOffset": 8289, "count": 5}, {"startOffset": 8526, "endOffset": 8564, "count": 5}, {"startOffset": 8564, "endOffset": 8683, "count": 0}, {"startOffset": 8683, "endOffset": 8882, "count": 5}, {"startOffset": 8882, "endOffset": 9701, "count": 1}, {"startOffset": 9407, "endOffset": 9424, "count": 0}, {"startOffset": 9588, "endOffset": 9683, "count": 0}, {"startOffset": 9715, "endOffset": 9811, "count": 6}, {"startOffset": 9811, "endOffset": 10000, "count": 4}, {"startOffset": 9907, "endOffset": 9935, "count": 0}, {"startOffset": 10000, "endOffset": 10153, "count": 1}, {"startOffset": 10164, "endOffset": 10241, "count": 0}, {"startOffset": 10241, "endOffset": 10352, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4479, "endOffset": 4496, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4901, "endOffset": 4916, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4922, "endOffset": 4949, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8757, "endOffset": 8861, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8968, "endOffset": 9072, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17371, "endOffset": 19089, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23132, "endOffset": 23149, "count": 196}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23952, "endOffset": 26493, "count": 196}, {"startOffset": 24948, "endOffset": 26144, "count": 40}], "isBlockCoverage": true}, {"functionName": "onChange", "ranges": [{"startOffset": 24435, "endOffset": 24472, "count": 142}], "isBlockCoverage": true}, {"functionName": "onClick", "ranges": [{"startOffset": 25199, "endOffset": 25219, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2316", "url": "file:///E:/FX/scraping-analysis-marketing/src/components/ui/button.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6746, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6746, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1997, "endOffset": 2532, "count": 392}, {"startOffset": 2088, "endOffset": 2116, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2661, "endOffset": 2683, "count": 392}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2789, "endOffset": 2819, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2318", "url": "file:///E:/FX/scraping-analysis-marketing/src/components/ui/input.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3295, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3295, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 571, "endOffset": 1313, "count": 196}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1439, "endOffset": 1460, "count": 196}], "isBlockCoverage": true}]}, {"scriptId": "2319", "url": "file:///E:/FX/scraping-analysis-marketing/src/components/ui/label.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4795, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4795, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1366, "endOffset": 1810, "count": 176}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1967, "endOffset": 1988, "count": 176}], "isBlockCoverage": true}]}, {"scriptId": "2321", "url": "file:///E:/FX/scraping-analysis-marketing/src/components/ui/card.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 570, "endOffset": 972, "count": 176}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1068, "endOffset": 1442, "count": 176}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1549, "endOffset": 1959, "count": 176}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2070, "endOffset": 2442, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2561, "endOffset": 2914, "count": 176}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3024, "endOffset": 3395, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3530, "endOffset": 3550, "count": 176}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3652, "endOffset": 3678, "count": 176}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3780, "endOffset": 3806, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3907, "endOffset": 3932, "count": 176}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4039, "endOffset": 4070, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4173, "endOffset": 4200, "count": 176}], "isBlockCoverage": true}]}, {"scriptId": "2324", "url": "file:///E:/FX/scraping-analysis-marketing/src/hooks/useErrorHandler.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4887, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4887, "count": 1}], "isBlockCoverage": true}, {"functionName": "useErrorHandler", "ranges": [{"startOffset": 586, "endOffset": 1764, "count": 176}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 648, "endOffset": 1019, "count": 3}, {"startOffset": 857, "endOffset": 876, "count": 0}, {"startOffset": 913, "endOffset": 936, "count": 1}, {"startOffset": 937, "endOffset": 948, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1084, "endOffset": 1234, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1299, "endOffset": 1449, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1511, "endOffset": 1658, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1869, "endOffset": 1900, "count": 176}], "isBlockCoverage": true}]}, {"scriptId": "2326", "url": "file:///E:/FX/scraping-analysis-marketing/src/lib/errors.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9834, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9834, "count": 1}], "isBlockCoverage": true}, {"functionName": "_define_property", "ranges": [{"startOffset": 223, "endOffset": 521, "count": 9}, {"startOffset": 288, "endOffset": 464, "count": 0}], "isBlockCoverage": true}, {"functionName": "AppError", "ranges": [{"startOffset": 557, "endOffset": 908, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1007, "endOffset": 1031, "count": 0}], "isBlockCoverage": false}, {"functionName": "NetworkError", "ranges": [{"startOffset": 1077, "endOffset": 1281, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1384, "endOffset": 1412, "count": 0}], "isBlockCoverage": false}, {"functionName": "ValidationError", "ranges": [{"startOffset": 1461, "endOffset": 1631, "count": 2}, {"startOffset": 1556, "endOffset": 1584, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1737, "endOffset": 1768, "count": 2}], "isBlockCoverage": true}, {"functionName": "SupabaseError", "ranges": [{"startOffset": 1815, "endOffset": 2002, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2106, "endOffset": 2135, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleError", "ranges": [{"startOffset": 2159, "endOffset": 2816, "count": 3}, {"startOffset": 2205, "endOffset": 2234, "count": 2}, {"startOffset": 2234, "endOffset": 2690, "count": 1}, {"startOffset": 2353, "endOffset": 2416, "count": 0}, {"startOffset": 2503, "endOffset": 2567, "count": 0}, {"startOffset": 2690, "endOffset": 2815, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2917, "endOffset": 2944, "count": 3}], "isBlockCoverage": true}, {"functionName": "logError", "ranges": [{"startOffset": 2965, "endOffset": 3446, "count": 3}, {"startOffset": 3294, "endOffset": 3444, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3544, "endOffset": 3568, "count": 3}], "isBlockCoverage": true}, {"functionName": "createErrorHandler", "ranges": [{"startOffset": 3599, "endOffset": 3749, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3857, "endOffset": 3891, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2327", "url": "file:///E:/FX/scraping-analysis-marketing/src/components/ui/progress.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4745, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4745, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 689, "endOffset": 1815, "count": 7}, {"startOffset": 1251, "endOffset": 1268, "count": 0}, {"startOffset": 1292, "endOffset": 1310, "count": 0}, {"startOffset": 1332, "endOffset": 1351, "count": 0}, {"startOffset": 1430, "endOffset": 1434, "count": 6}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1978, "endOffset": 2002, "count": 7}], "isBlockCoverage": true}]}, {"scriptId": "2329", "url": "file:///E:/FX/scraping-analysis-marketing/src/components/ui/badge.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5965, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5965, "count": 1}], "isBlockCoverage": true}, {"functionName": "Badge", "ranges": [{"startOffset": 1851, "endOffset": 2280, "count": 352}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2375, "endOffset": 2396, "count": 352}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2501, "endOffset": 2530, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2330", "url": "file:///E:/FX/scraping-analysis-marketing/src/hooks/useSecurity.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 39070, "count": 1}], "isBlockCoverage": true}, {"functionName": "useURLValidation", "ranges": [{"startOffset": 580, "endOffset": 1982, "count": 176}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 668, "endOffset": 1135, "count": 7}, {"startOffset": 775, "endOffset": 909, "count": 1}, {"startOffset": 982, "endOffset": 1035, "count": 6}, {"startOffset": 1036, "endOffset": 1042, "count": 1}, {"startOffset": 1071, "endOffset": 1077, "count": 6}, {"startOffset": 1078, "endOffset": 1118, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1201, "endOffset": 1887, "count": 6}, {"startOffset": 1503, "endOffset": 1720, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1244, "endOffset": 1325, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1369, "endOffset": 1393, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1439, "endOffset": 1464, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1673, "endOffset": 1683, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1775, "endOffset": 1794, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1838, "endOffset": 1848, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2087, "endOffset": 2119, "count": 176}], "isBlockCoverage": true}, {"functionName": "useInputSanitization", "ranges": [{"startOffset": 2162, "endOffset": 3679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3788, "endOffset": 3824, "count": 0}], "isBlockCoverage": false}, {"functionName": "useRateLimit", "ranges": [{"startOffset": 3862, "endOffset": 5132, "count": 176}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3968, "endOffset": 4045, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4337, "endOffset": 4755, "count": 7}, {"startOffset": 4418, "endOffset": 4607, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4902, "endOffset": 4965, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5233, "endOffset": 5261, "count": 176}], "isBlockCoverage": true}, {"functionName": "useXSSProtection", "ranges": [{"startOffset": 5300, "endOffset": 6105, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6210, "endOffset": 6242, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSecurityMonitoring", "ranges": [{"startOffset": 6286, "endOffset": 8955, "count": 176}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6524, "endOffset": 7446, "count": 9}], "isBlockCoverage": true}, {"functionName": "updateEvents", "ranges": [{"startOffset": 6559, "endOffset": 7278, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6816, "endOffset": 6871, "count": 48}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7116, "endOffset": 7167, "count": 48}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7412, "endOffset": 7439, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7516, "endOffset": 7634, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7705, "endOffset": 7835, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7907, "endOffset": 7983, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8055, "endOffset": 8745, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9065, "endOffset": 9102, "count": 176}], "isBlockCoverage": true}, {"functionName": "useSecureForm", "ranges": [{"startOffset": 9149, "endOffset": 11841, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11943, "endOffset": 11972, "count": 0}], "isBlockCoverage": false}, {"functionName": "useContentSecurityPolicy", "ranges": [{"startOffset": 12020, "endOffset": 13286, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 13399, "endOffset": 13439, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2331", "url": "file:///E:/FX/scraping-analysis-marketing/src/lib/security.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41016, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41016, "count": 1}], "isBlockCoverage": true}, {"functionName": "_define_property", "ranges": [{"startOffset": 275, "endOffset": 573, "count": 8}, {"startOffset": 340, "endOffset": 516, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidURL", "ranges": [{"startOffset": 606, "endOffset": 1409, "count": 7}, {"startOffset": 776, "endOffset": 821, "count": 0}, {"startOffset": 821, "endOffset": 1005, "count": 6}, {"startOffset": 1005, "endOffset": 1050, "count": 0}, {"startOffset": 1050, "endOffset": 1138, "count": 6}, {"startOffset": 1138, "endOffset": 1183, "count": 0}, {"startOffset": 1183, "endOffset": 1278, "count": 6}, {"startOffset": 1278, "endOffset": 1323, "count": 0}, {"startOffset": 1323, "endOffset": 1359, "count": 6}, {"startOffset": 1359, "endOffset": 1403, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 965, "endOffset": 1002, "count": 48}], "isBlockCoverage": true}, {"functionName": "sanitizeURL", "ranges": [{"startOffset": 1421, "endOffset": 2028, "count": 6}, {"startOffset": 1953, "endOffset": 2022, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1738, "endOffset": 1813, "count": 24}], "isBlockCoverage": true}, {"functionName": "isPrivateIP", "ranges": [{"startOffset": 2040, "endOffset": 2341, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2304, "endOffset": 2333, "count": 36}], "isBlockCoverage": true}, {"functionName": "hasSuspiciousPatterns", "ranges": [{"startOffset": 2353, "endOffset": 2696, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2660, "endOffset": 2688, "count": 48}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2799, "endOffset": 2827, "count": 13}], "isBlockCoverage": true}, {"functionName": "sanitizeHTML", "ranges": [{"startOffset": 3173, "endOffset": 3996, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeText", "ranges": [{"startOffset": 4008, "endOffset": 4353, "count": 142}], "isBlockCoverage": true}, {"functionName": "validateEmail", "ranges": [{"startOffset": 4365, "endOffset": 4534, "count": 0}], "isBlockCoverage": false}, {"functionName": "validatePhoneNumber", "ranges": [{"startOffset": 4546, "endOffset": 4674, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeFilename", "ranges": [{"startOffset": 4686, "endOffset": 5249, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5354, "endOffset": 5384, "count": 0}], "isBlockCoverage": false}, {"functionName": "getInstance", "ranges": [{"startOffset": 5452, "endOffset": 5666, "count": 9}, {"startOffset": 5532, "endOffset": 5620, "count": 1}], "isBlockCoverage": true}, {"functionName": "isAllowed", "ranges": [{"startOffset": 5671, "endOffset": 6045, "count": 7}, {"startOffset": 6017, "endOffset": 6044, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5812, "endOffset": 5846, "count": 21}], "isBlockCoverage": true}, {"functionName": "getRemainingRequests", "ranges": [{"startOffset": 6050, "endOffset": 6263, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6152, "endOffset": 6186, "count": 28}], "isBlockCoverage": true}, {"functionName": "getResetTime", "ranges": [{"startOffset": 6268, "endOffset": 6389, "count": 7}, {"startOffset": 6325, "endOffset": 6334, "count": 0}], "isBlockCoverage": true}, {"functionName": "RateLimiter", "ranges": [{"startOffset": 6394, "endOffset": 6664, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6766, "endOffset": 6793, "count": 9}], "isBlockCoverage": true}, {"functionName": "generateNonce", "ranges": [{"startOffset": 6927, "endOffset": 7118, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCSPHeader", "ranges": [{"startOffset": 7130, "endOffset": 7841, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7942, "endOffset": 7968, "count": 0}], "isBlockCoverage": false}, {"functionName": "escapeHTML", "ranges": [{"startOffset": 8041, "endOffset": 8178, "count": 0}], "isBlockCoverage": false}, {"functionName": "unescapeHTML", "ranges": [{"startOffset": 8190, "endOffset": 8352, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeForAttribute", "ranges": [{"startOffset": 8364, "endOffset": 8513, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidJSON", "ranges": [{"startOffset": 8525, "endOffset": 8672, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8776, "endOffset": 8805, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 8878, "endOffset": 9204, "count": 0}], "isBlockCoverage": false}, {"functionName": "getItem", "ranges": [{"startOffset": 9216, "endOffset": 9589, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 9601, "endOffset": 9662, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 9674, "endOffset": 9719, "count": 0}], "isBlockCoverage": false}, {"functionName": "encrypt", "ranges": [{"startOffset": 9731, "endOffset": 10028, "count": 0}], "isBlockCoverage": false}, {"functionName": "decrypt", "ranges": [{"startOffset": 10040, "endOffset": 10379, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10483, "endOffset": 10512, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateResponse", "ranges": [{"startOffset": 10661, "endOffset": 10918, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSecurityScore", "ranges": [{"startOffset": 10930, "endOffset": 11372, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11478, "endOffset": 11509, "count": 0}], "isBlockCoverage": false}, {"functionName": "log", "ranges": [{"startOffset": 11591, "endOffset": 12098, "count": 8}, {"startOffset": 11837, "endOffset": 11896, "count": 0}, {"startOffset": 11990, "endOffset": 12092, "count": 0}], "isBlockCoverage": true}, {"functionName": "getLogs", "ranges": [{"startOffset": 12110, "endOffset": 12293, "count": 10}, {"startOffset": 12152, "endOffset": 12234, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12190, "endOffset": 12222, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearLogs", "ranges": [{"startOffset": 12305, "endOffset": 12348, "count": 0}], "isBlockCoverage": false}, {"functionName": "exportLogs", "ranges": [{"startOffset": 12360, "endOffset": 12431, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 12535, "endOffset": 12564, "count": 18}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 12778, "endOffset": 12806, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 12951, "endOffset": 12981, "count": 142}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 13123, "endOffset": 13152, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 13294, "endOffset": 13323, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 13471, "endOffset": 13502, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 13644, "endOffset": 13673, "count": 0}], "isBlockCoverage": false}]}]}