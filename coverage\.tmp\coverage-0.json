{"result": [{"scriptId": "1319", "url": "file:///E:/FX/scraping-analysis-marketing/src/test/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14658, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14658, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 329, "endOffset": 766, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 796, "endOffset": 4687, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4723, "endOffset": 5159, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5859, "endOffset": 5954, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6010, "endOffset": 6105, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6231, "endOffset": 6522, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6571, "endOffset": 6649, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6695, "endOffset": 6801, "count": 19}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6850, "endOffset": 6884, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1939", "url": "file:///E:/FX/scraping-analysis-marketing/src/test/mocks/server.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7060, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7060, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 604, "endOffset": 675, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 752, "endOffset": 858, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 936, "endOffset": 1042, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1121, "endOffset": 1192, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1299, "endOffset": 1768, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1870, "endOffset": 2122, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2217, "endOffset": 2653, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2754, "endOffset": 2778, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2939, "endOffset": 2961, "count": 21}], "isBlockCoverage": true}]}, {"scriptId": "2024", "url": "file:///E:/FX/scraping-analysis-marketing/src/lib/__tests__/scraping.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30905, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30905, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 506, "endOffset": 11555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 619, "endOffset": 831, "count": 19}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 887, "endOffset": 2442, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 953, "endOffset": 1335, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 1059, "endOffset": 1154, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1399, "endOffset": 1783, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 1505, "endOffset": 1600, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1848, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2230, "endOffset": 2434, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2496, "endOffset": 7183, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2563, "endOffset": 2973, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 2767, "endOffset": 2792, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3038, "endOffset": 3580, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 3367, "endOffset": 3392, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3637, "endOffset": 4332, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 3981, "endOffset": 4006, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4386, "endOffset": 5103, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 4755, "endOffset": 4780, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5158, "endOffset": 5907, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 5547, "endOffset": 5572, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5968, "endOffset": 6487, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 6292, "endOffset": 6317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6548, "endOffset": 7175, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 6872, "endOffset": 6897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7239, "endOffset": 8463, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7304, "endOffset": 7591, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7650, "endOffset": 8095, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8158, "endOffset": 8455, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 8264, "endOffset": 8287, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8526, "endOffset": 9144, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8601, "endOffset": 9136, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 8865, "endOffset": 8890, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9195, "endOffset": 9986, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9269, "endOffset": 9588, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9659, "endOffset": 9978, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10042, "endOffset": 11551, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10106, "endOffset": 10751, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 10208, "endOffset": 10303, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10833, "endOffset": 11543, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 10939, "endOffset": 11023, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "2025", "url": "file:///E:/FX/scraping-analysis-marketing/src/lib/scraping.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 48744, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 48744, "count": 1}], "isBlockCoverage": true}, {"functionName": "_define_property", "ranges": [{"startOffset": 224, "endOffset": 522, "count": 20}, {"startOffset": 289, "endOffset": 465, "count": 0}], "isBlockCoverage": true}, {"functionName": "scrapeWithAPI", "ranges": [{"startOffset": 599, "endOffset": 1520, "count": 2}, {"startOffset": 865, "endOffset": 1376, "count": 0}, {"startOffset": 1483, "endOffset": 1500, "count": 0}], "isBlockCoverage": true}, {"functionName": "scrapeUrl", "ranges": [{"startOffset": 1525, "endOffset": 5218, "count": 21}, {"startOffset": 1911, "endOffset": 2214, "count": 1}, {"startOffset": 2171, "endOffset": 2214, "count": 0}, {"startOffset": 2214, "endOffset": 2348, "count": 19}, {"startOffset": 2348, "endOffset": 2371, "count": 18}, {"startOffset": 2401, "endOffset": 2467, "count": 2}, {"startOffset": 2467, "endOffset": 2766, "count": 17}, {"startOffset": 2766, "endOffset": 2801, "count": 15}, {"startOffset": 2801, "endOffset": 2908, "count": 1}, {"startOffset": 2908, "endOffset": 3044, "count": 14}, {"startOffset": 3044, "endOffset": 3934, "count": 3}, {"startOffset": 3411, "endOffset": 3430, "count": 1}, {"startOffset": 3430, "endOffset": 3920, "count": 2}, {"startOffset": 3934, "endOffset": 4051, "count": 15}, {"startOffset": 4051, "endOffset": 4140, "count": 1}, {"startOffset": 4140, "endOffset": 4392, "count": 15}, {"startOffset": 4392, "endOffset": 5212, "count": 3}, {"startOffset": 4699, "endOffset": 4725, "count": 0}, {"startOffset": 4758, "endOffset": 5129, "count": 0}, {"startOffset": 5130, "endOffset": 5156, "count": 0}], "isBlockCoverage": true}, {"functionName": "fetchWithTimeout", "ranges": [{"startOffset": 5223, "endOffset": 5801, "count": 27}, {"startOffset": 5700, "endOffset": 5740, "count": 25}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5349, "endOffset": 5371, "count": 0}], "isBlockCoverage": false}, {"functionName": "fetchWithCorsProxy", "ranges": [{"startOffset": 5806, "endOffset": 7620, "count": 3}, {"startOffset": 5922, "endOffset": 7508, "count": 10}, {"startOffset": 6128, "endOffset": 6801, "count": 3}, {"startOffset": 6315, "endOffset": 6427, "count": 0}, {"startOffset": 6427, "endOffset": 6483, "count": 1}, {"startOffset": 6483, "endOffset": 6783, "count": 0}, {"startOffset": 6801, "endOffset": 7326, "count": 7}, {"startOffset": 7017, "endOffset": 7125, "count": 0}, {"startOffset": 7125, "endOffset": 7326, "count": 1}, {"startOffset": 7341, "endOffset": 7498, "count": 9}, {"startOffset": 7508, "endOffset": 7591, "count": 2}, {"startOffset": 7592, "endOffset": 7610, "count": 0}], "isBlockCoverage": true}, {"functionName": "parseHtml", "ranges": [{"startOffset": 7625, "endOffset": 11164, "count": 15}, {"startOffset": 7932, "endOffset": 7951, "count": 14}, {"startOffset": 7954, "endOffset": 7959, "count": 1}, {"startOffset": 8074, "endOffset": 8088, "count": 1}, {"startOffset": 8100, "endOffset": 8105, "count": 14}, {"startOffset": 8453, "endOffset": 8561, "count": 105}, {"startOffset": 8545, "endOffset": 8551, "count": 15}, {"startOffset": 8661, "endOffset": 8672, "count": 0}, {"startOffset": 10407, "endOffset": 10421, "count": 0}, {"startOffset": 10494, "endOffset": 10508, "count": 0}, {"startOffset": 10586, "endOffset": 10600, "count": 0}, {"startOffset": 10677, "endOffset": 10691, "count": 0}, {"startOffset": 10783, "endOffset": 10797, "count": 0}, {"startOffset": 10877, "endOffset": 10891, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8911, "endOffset": 8937, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9119, "endOffset": 9437, "count": 2}, {"startOffset": 9206, "endOffset": 9218, "count": 0}, {"startOffset": 9364, "endOffset": 9423, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9728, "endOffset": 10046, "count": 2}, {"startOffset": 9816, "endOffset": 9828, "count": 0}, {"startOffset": 9973, "endOffset": 10032, "count": 0}], "isBlockCoverage": true}, {"functionName": "extractTextContent", "ranges": [{"startOffset": 11169, "endOffset": 11664, "count": 15}, {"startOffset": 11457, "endOffset": 11462, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11344, "endOffset": 11369, "count": 0}], "isBlockCoverage": false}, {"functionName": "count<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 11669, "endOffset": 11884, "count": 15}, {"startOffset": 11707, "endOffset": 11716, "count": 5}, {"startOffset": 11716, "endOffset": 11883, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11824, "endOffset": 11847, "count": 30}], "isBlockCoverage": true}, {"functionName": "scrapeUrls", "ranges": [{"startOffset": 11918, "endOffset": 12794, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12254, "endOffset": 12289, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12399, "endOffset": 12752, "count": 4}, {"startOffset": 12536, "endOffset": 12738, "count": 0}], "isBlockCoverage": true}, {"functionName": "generateDemoData", "ranges": [{"startOffset": 12837, "endOffset": 15087, "count": 2}, {"startOffset": 12949, "endOffset": 13008, "count": 0}], "isBlockCoverage": true}, {"functionName": "WebScraper", "ranges": [{"startOffset": 15092, "endOffset": 15654, "count": 20}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 15755, "endOffset": 15781, "count": 19}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 15951, "endOffset": 15977, "count": 0}], "isBlockCoverage": false}]}]}